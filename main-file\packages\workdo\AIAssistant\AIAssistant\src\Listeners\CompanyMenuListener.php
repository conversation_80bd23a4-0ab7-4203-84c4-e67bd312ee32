<?php

namespace Workdo\AIAssistant\Listeners;

use App\Events\CompanyMenuEvent;

class CompanyMenuListener
{
    /**
     * Handle the event.
     */
    public function handle(CompanyMenuEvent $event): void
    {
        $module = 'AIAssistant';
        $menu = $event->menu;
        $menu->add([
            'category' => 'Communication',
            'title' => __('AI Assistant'),
            'icon' => 'robot',
            'name' => 'ai-assistant',
            'parent' => null,
            'order' => 1400,
            'ignore_if' => [],
            'depend_on' => [],
            'route' => 'ai-assistant.index',
            'module' => $module,
            'permission' => 'ai assistant manage'
        ]);
    }
}
