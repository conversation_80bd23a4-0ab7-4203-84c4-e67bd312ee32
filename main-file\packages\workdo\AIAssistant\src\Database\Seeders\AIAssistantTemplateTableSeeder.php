<?php

namespace Workdo\AIAssistant\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AddOn;
use Illuminate\Support\Facades\File;

class AIAssistantTemplateTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        // Get all enabled addons from the database
        $enabledAddons = AddOn::where('is_enable', 1)->pluck('module')->toArray();

        if(module_is_active('AIAssistant'))
        {
            foreach ($enabledAddons as $addonName) {
                // Check if the addon has an AIAssistantTemplateListTableSeeder
                $seederPath = base_path('packages/workdo/' . $addonName . '/src/Database/Seeders/AIAssistantTemplateListTableSeeder.php');

                if(File::exists($seederPath)) {
                    $seederClass = "\Workdo\\{$addonName}\\Database\\Seeders\\AIAssistantTemplateListTableSeeder";

                    // Check if the class exists before calling it
                    if(class_exists($seederClass)) {
                        $this->call($seederClass);
                    }
                }
            }
        }
    }
}
