<?php

// Standalone script to register AIAssistant addon
// Run this from the root directory: php register_aiassistant.php

require_once 'main-file/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'main-file/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\AddOn;

try {
    // Check if AIAssistant addon already exists
    $addon = AddOn::where('module', 'AIAssistant')->first();

    if (!$addon) {
        // Create the AIAssistant addon entry
        $addon = new AddOn();
        $addon->module = 'AIAssistant';
        $addon->name = 'AI Assistant';
        $addon->monthly_price = 5;
        $addon->yearly_price = 50;
        $addon->is_enable = 1; // Enable by default
        $addon->package_name = 'aiassistant';
        $addon->save();

        echo "✅ AIAssistant addon registered successfully!\n";
        echo "   Module: {$addon->module}\n";
        echo "   Name: {$addon->name}\n";
        echo "   Package Name: {$addon->package_name}\n";
        echo "   Status: " . ($addon->is_enable ? 'Enabled' : 'Disabled') . "\n";
    } else {
        // Update existing addon with package_name if missing
        $updated = false;
        if (empty($addon->package_name)) {
            $addon->package_name = 'aiassistant';
            $updated = true;
        }
        if (!$addon->is_enable) {
            $addon->is_enable = 1;
            $updated = true;
        }

        if ($updated) {
            $addon->save();
            echo "✅ AIAssistant addon updated successfully!\n";
        } else {
            echo "ℹ️  AIAssistant addon already exists and is properly configured.\n";
        }

        echo "   Module: {$addon->module}\n";
        echo "   Name: {$addon->name}\n";
        echo "   Package Name: {$addon->package_name}\n";
        echo "   Status: " . ($addon->is_enable ? 'Enabled' : 'Disabled') . "\n";
    }

    // Clear any module cache
    if (class_exists('App\Classes\Module')) {
        $module = new \App\Classes\Module();
        $module->moduleCacheForget('AIAssistant');
        echo "🔄 Module cache cleared.\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   Please check your database connection and try again.\n";
}
