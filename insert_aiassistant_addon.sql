-- SQL script to manually insert AIAssistant addon into add_ons table
-- Run this in your database management tool (phpMyAdmin, etc.)

-- First check if the addon already exists
SELECT * FROM add_ons WHERE module = 'AIAssistant';

-- If it doesn't exist, insert it
INSERT INTO add_ons (module, name, monthly_price, yearly_price, image, is_enable, package_name, created_at, updated_at)
SELECT 'AIAssistant', 'AI Assistant', '5', '50', NULL, 1, 'aiassistant', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM add_ons WHERE module = 'AIAssistant');

-- If it exists but package_name is missing, update it
UPDATE add_ons 
SET package_name = 'aiassistant', 
    is_enable = 1,
    updated_at = NOW()
WHERE module = 'AIAssistant' 
AND (package_name IS NULL OR package_name = '');

-- Verify the result
SELECT * FROM add_ons WHERE module = 'AIAssistant';
