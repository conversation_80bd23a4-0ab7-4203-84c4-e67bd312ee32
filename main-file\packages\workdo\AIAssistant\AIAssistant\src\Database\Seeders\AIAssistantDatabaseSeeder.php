<?php

namespace Workdo\AIAssistant\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AddOn;

class AIAssistantDatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        // Ensure AIAssistant addon is registered in the database
        $this->ensureAddonRegistered();

        // Create permissions
        $this->call(PermissionTableSeeder::class);

        $this->call(AIAssistantTemplateTableSeeder::class);
        if(module_is_active('LandingPage'))
        {
            $this->call(MarketPlaceSeederTableSeeder::class);
        }
        // $this->call("OthersTableSeeder");
    }

    /**
     * Ensure the AIAssistant addon is registered in the add_ons table
     */
    private function ensureAddonRegistered()
    {
        $addon = AddOn::where('module', 'AIAssistant')->first();

        if (!$addon) {
            // Create the AIAssistant addon entry
            $addon = new AddOn();
            $addon->module = 'AIAssistant';
            $addon->name = 'AI Assistant';
            $addon->monthly_price = 5;
            $addon->yearly_price = 50;
            $addon->is_enable = 1;
            $addon->package_name = 'aiassistant';
            $addon->save();
        } else {
            // Update existing addon with package_name if missing
            if (empty($addon->package_name)) {
                $addon->package_name = 'aiassistant';
                $addon->save();
            }
        }
    }
}
