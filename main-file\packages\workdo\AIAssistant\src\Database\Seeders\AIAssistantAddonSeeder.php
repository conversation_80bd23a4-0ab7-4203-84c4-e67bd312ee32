<?php

namespace Workdo\AIAssistant\Database\Seeders;

use App\Models\AddOn;
use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;

class AIAssistantAddonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        // Check if AIAssistant addon already exists
        $addon = AddOn::where('module', 'AIAssistant')->first();
        
        if (!$addon) {
            // Create the AIAssistant addon entry
            $addon = new AddOn();
            $addon->module = 'AIAssistant';
            $addon->name = 'AI Assistant';
            $addon->monthly_price = 5;
            $addon->yearly_price = 50;
            $addon->is_enable = 1; // Enable by default
            $addon->package_name = 'aiassistant';
            $addon->save();
        } else {
            // Update existing addon with package_name if missing
            if (empty($addon->package_name)) {
                $addon->package_name = 'aiassistant';
                $addon->save();
            }
        }
    }
}
